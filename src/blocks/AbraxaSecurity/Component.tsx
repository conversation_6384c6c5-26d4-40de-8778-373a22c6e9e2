import { cn } from '@/utilities/ui'
import React from 'react'
import RichText from '@/components/RichText'

import type { AbraxaSecurityBlock as AbraxaSecurityBlockProps } from '@/payload-types'
import { Media } from '@/components/Media'

export const AbraxaSecurityBlock: React.FC<AbraxaSecurityBlockProps> = (props) => {
  const { columns, className } = props

  const colsSpanClasses = {
    full: '12',
    half: '6',
    oneFourth: '3',
    oneThird: '4',
    twoThirds: '8',
    fiveTwelfths: '5',
    sevenTwelfths: '7',
  }

  return (
    <div className={cn('container py-20', className)}>
      <div className="grid grid-cols-4 lg:grid-cols-12 gap-y-20 gap-x-8 items-center">
        {columns &&
          columns.length > 0 &&
          columns.map((col, index) => {
            const { richText, size, className, items } = col

            return (
              <div
                className={cn(`col-span-4 lg:col-span-${colsSpanClasses[size!]}`, className, {
                  'md:col-span-2': size !== 'full',
                })}
                key={index}
              >
                {richText && <RichText data={richText} enableGutter={false} />}

                <div className="grid lg:grid-cols-6 gap-8">
                  {items &&
                    items.length > 0 &&
                    items?.map((item, index) => {
                      const { title, description, media } = item

                      return (
                        <div
                          className="AbraxaCard p-8 rounded-lg flex flex-col gap-4 items-center col-span-3"
                          key={index}
                        >
                          {media && typeof media === 'object' && (
                            <Media imgClassName="" size="size-8" priority resource={media} />
                          )}
                          <div className="flex flex-col gap-1 items-center text-center">
                            <div className="font-semibold text-secondary-foreground">{title}</div>
                            <div className="text-tertiary-foreground text-sm">{description}</div>
                          </div>
                        </div>
                      )
                    })}
                </div>
              </div>
            )
          })}
      </div>
    </div>
  )
}
