import React, { Fragment } from 'react'

import type { Page } from '@/payload-types'

import { AbraxaCommandBlock } from '@/blocks/AbraxaCommand/Component'
import { AbraxaPartnersBlock } from '@/blocks/AbraxaPartners/Component'
import { AbraxaSliderBlock } from '@/blocks/AbraxaSlider/Component'
import { AbraxaTabsBlock } from '@/blocks/AbraxaTabs/Component'
import { ArchiveBlock } from '@/blocks/ArchiveBlock/Component'
import { CallToActionBlock } from '@/blocks/CallToAction/Component'
import { ContentBlock } from '@/blocks/Content/Component'
import { FormBlock } from '@/blocks/Form/Component'
import { MediaBlock } from '@/blocks/MediaBlock/Component'

const blockComponents = {
  abraxaPartners: AbraxaPartnersBlock,
  abraxaCommand: AbraxaCommandBlock,
  abraxaSlider: AbraxaSliderBlock,
  abraxaTabs: AbraxaTabsBlock,
  archive: ArchiveBlock,
  content: ContentBlock,
  cta: CallToActionBlock,
  formBlock: FormBlock,
  mediaBlock: MediaBlock,
}

export const RenderBlocks: React.FC<{
  blocks: Page['layout'][0][]
}> = (props) => {
  const { blocks } = props

  const hasBlocks = blocks && Array.isArray(blocks) && blocks.length > 0

  if (hasBlocks) {
    return (
      <Fragment>
        {blocks.map((block, index) => {
          const { blockType } = block

          if (blockType && blockType in blockComponents) {
            const Block = blockComponents[blockType]

            if (Block) {
              return (
                <div key={index} className={`block-${blockType}`}>
                  {/* @ts-expect-error there may be some mismatch between the expected types here */}
                  <Block {...block} disableInnerContainer />
                </div>
              )
            }
          }
          return null
        })}
      </Fragment>
    )
  }

  return null
}
