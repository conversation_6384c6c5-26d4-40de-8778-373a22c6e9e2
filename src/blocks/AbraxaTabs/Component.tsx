'use client'

import { cn } from '@/utilities/ui'
import React, { useState } from 'react'
import RichText from '@/components/RichText'
import { motion, AnimatePresence } from 'framer-motion'

import type { AbraxaTabsBlock as AbraxaTabsBlockProps } from '@/payload-types'

import { CMSLink } from '@/components/Link'
import { Media } from '@/components/Media'
import { Separator } from '@/components/ui/separator'

export const AbraxaTabsBlock: React.FC<AbraxaTabsBlockProps> = (props) => {
  const { tabs, title } = props
  const [activeTab, setActiveTab] = useState(0)
  const [previousTab, setPreviousTab] = useState(0)

  const handleTabChange = (newTabIndex: number) => {
    setPreviousTab(activeTab)
    setActiveTab(newTabIndex)
  }

  // Determine animation direction based on tab navigation
  const isMovingForward = activeTab > previousTab
  const isMovingBackward = activeTab < previousTab

  // Animation variants based on direction
  const getAnimationVariants = () => {
    if (isMovingForward) {
      // Next tab: slide from bottom to top
      return {
        initial: { y: 32, opacity: 0 },
        animate: { y: 0, opacity: 1 },
        exit: { y: -32, opacity: 0 },
      }
    } else if (isMovingBackward) {
      // Previous tab: slide from top to bottom
      return {
        initial: { y: -32, opacity: 0 },
        animate: { y: 0, opacity: 1 },
        exit: { y: 32, opacity: 0 },
      }
    } else {
      // Default/initial state
      return {
        initial: { y: 32, opacity: 0 },
        animate: { y: 0, opacity: 1 },
        exit: { y: -32, opacity: 0 },
      }
    }
  }

  const animationVariants = getAnimationVariants()

  const colsSpanClasses = {
    full: '12',
    half: '6',
    oneFourth: '3',
    oneThird: '4',
    twoThirds: '8',
  }

  if (!tabs || tabs.length === 0) {
    return null
  }

  return (
    <div className="container py-20">
      {title && (
        <div className="grid grid-cols-4 lg:grid-cols-12 gap-x-8 mb-20">
          <RichText className="col-span-4 lg:col-span-9" data={title} enableGutter={false} />
        </div>
      )}
      <div className="grid grid-cols-12 gap-x-8 min-h-[400px] overflow-hidden gradient-primary">
        {/* Vertical Tab Navigation */}
        <div className="lg:col-span-4 md:mt-32">
          <div className="flex flex-col space-y-2">
            {tabs.map((tab, index) => (
              <motion.div
                key={index}
                onClick={() => handleTabChange(index)}
                onHoverStart={() => handleTabChange(index)}
                className={cn(
                  'font-bold text-2xl transition-all duration-900 hover:text-primary-foreground cursor-pointer flex items-center gap-4',
                  {
                    'text-primary-foreground ': activeTab === index,
                    'text-slate-500 hover:text-primary-foreground': activeTab !== index,
                  },
                )}
              >
                <div
                  className={cn('w-1.5 h-10 rounded-full bg-current transition-all duration-900', {
                    'bg-primary': activeTab === index,
                    'bg-secondary': activeTab !== index,
                  })}
                />
                {tab.tabName}
              </motion.div>
            ))}
          </div>
        </div>

        {/* Tab Content Area */}
        <div className="lg:col-span-7 lg:col-end-13 relative overflow-hidden ">
          <AnimatePresence mode="wait">
            <motion.div
              key={activeTab}
              initial={animationVariants.initial}
              animate={animationVariants.animate}
              exit={animationVariants.exit}
              transition={{
                // type: 'spring',
                // stiffness: 100,
                // damping: 30,
                duration: 0.4,
              }}
              className="overflow-hidden inset-0"
            >
              <div className="flex flex-col gap-10">
                {tabs[activeTab]?.cover && typeof tabs[activeTab].cover === 'object' && (
                  <Media imgClassName="object-cover" priority resource={tabs[activeTab].cover} />
                )}
                {tabs[activeTab]?.title && (
                  <div>
                    <RichText data={tabs[activeTab]?.title} enableGutter={false} />
                  </div>
                )}

                <Separator />

                {tabs[activeTab] &&
                tabs[activeTab].columns &&
                tabs[activeTab].columns.length > 0 ? (
                  <div className="grid grid-cols-4 md:grid-cols-12 gap-x-8 gap-y-12">
                    {tabs[activeTab].columns.map((col, index) => {
                      const { enableLink, link, richText, size, media, className } = col

                      return (
                        <div
                          key={index}
                          className={cn(
                            `col-span-4 lg:col-span-${colsSpanClasses[size!]}`,
                            className,
                            {
                              'md:col-span-2': size !== 'full',
                            },
                          )}
                        >
                          <div className="flex gap-2">
                            <div className="size-6">
                              {media && typeof media === 'object' && (
                                <Media className="size-6" priority resource={media} />
                              )}
                            </div>

                            {richText && <RichText data={richText} enableGutter={false} />}
                          </div>

                          {enableLink && (
                            <div className="mt-4">
                              <CMSLink {...link} />
                            </div>
                          )}
                        </div>
                      )
                    })}
                  </div>
                ) : (
                  <div className="flex-1 flex items-center justify-center">
                    <p className="text-slate-400 text-lg">No content available for this tab</p>
                  </div>
                )}
              </div>
            </motion.div>
          </AnimatePresence>
        </div>
      </div>
    </div>
  )
}
