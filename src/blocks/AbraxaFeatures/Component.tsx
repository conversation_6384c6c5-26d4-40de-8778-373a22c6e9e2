import { cn } from '@/utilities/ui'
import React from 'react'
import RichText from '@/components/RichText'

import type { AbraxaFeaturesBlock as AbraxaFeaturesBlockProps } from '@/payload-types'

export const AbraxaFeaturesBlock: React.FC<AbraxaFeaturesBlockProps> = ({
  title,
  features,
  className,
}) => {
  return (
    <div className={cn('py-20', className)}>
      <div className="container">
        {title && (
          <div className="mb-12">
            <RichText data={title} enableGutter={false} />
          </div>
        )}
        <div className="grid gap-10">
          {features?.map((feature, index) => {
            return (
              <div key={index}>
                <RichText data={feature.richText} enableGutter={false} />
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}
