import { cn } from '@/utilities/ui'
import React from 'react'
import RichText from '@/components/RichText'

import type { AbraxaFeaturesBlock as AbraxaFeaturesBlockProps } from '@/payload-types'

import { Media } from '@/components/Media'
import { CMSLink } from '@/components/Link'

export const AbraxaFeaturesBlock: React.FC<AbraxaFeaturesBlockProps> = ({
  title,
  features,
  className,
  enableLink,
  link,
}) => {
  return (
    <div className={cn('container py-20', className)}>
      <div className="grid grid-cols-12 gap-x-8 gap-y-20">
        {title && (
          <div className="col-span-8 text-center lg:col-start-3">
            <RichText data={title} enableGutter={false} />
          </div>
        )}
        {features?.map((feature, index) => {
          return (
            <div className="grid gap-y-20 col-span-12 py-10" key={index}>
              <div className="AbraxaFeature grid grid-cols-12 gap-x-8 items-center">
                <div
                  className={cn('col-span-12 lg:col-span-5', {
                    'lg:order-last lg:col-end-13': feature.revertOrder,
                    '': !feature.revertOrder,
                  })}
                >
                  <div className="uppercase font-medium mb-4 text-quaternary-foreground">
                    {feature.preTitle && feature.preTitle}
                  </div>
                  {feature.richText && <RichText data={feature.richText} enableGutter={false} />}
                </div>
                <div
                  className={cn('col-span-12 lg:col-span-6', {
                    '': feature.revertOrder,
                    'lg:col-end-13': !feature.revertOrder,
                  })}
                >
                  {feature.media && typeof feature.media === 'object' && (
                    <Media imgClassName="" priority resource={feature.media} />
                  )}
                </div>
              </div>
            </div>
          )
        })}
        {enableLink && <CMSLink {...link} />}
      </div>
    </div>
  )
}

// className={cn('col-span-12 lg:col-span-6', {
//                     'lg:order-first': feature.revertOrder,
//                     'lg:col-end-13': !feature.revertOrder,
//                   })}
